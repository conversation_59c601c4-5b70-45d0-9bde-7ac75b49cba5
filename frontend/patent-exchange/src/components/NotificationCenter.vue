<template>
  <div class="notification-center">
    <!-- Notification Bell Icon -->
    <div class="position-relative">
      <button
        class="btn btn-outline-secondary position-relative"
        type="button"
        data-bs-toggle="dropdown"
        aria-expanded="false"
        @click="markAllAsRead"
      >
        <i class="bi bi-bell"></i>
        <span
          v-if="unreadCount > 0"
          class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger"
        >
          {{ unreadCount > 99 ? '99+' : unreadCount }}
        </span>
      </button>

      <!-- Notification Dropdown -->
      <div class="dropdown-menu dropdown-menu-end notification-dropdown" style="width: 350px;">
        <div class="dropdown-header d-flex justify-content-between align-items-center">
          <h6 class="mb-0">通知中心</h6>
          <div>
            <button
              v-if="unreadCount > 0"
              class="btn btn-sm btn-link text-decoration-none p-0 me-2"
              @click="markAllAsRead"
            >
              全部已读
            </button>
            <button
              class="btn btn-sm btn-link text-decoration-none p-0"
              @click="clearAll"
            >
              清空
            </button>
          </div>
        </div>

        <div class="dropdown-divider"></div>

        <!-- Notifications List -->
        <div class="notification-list" style="max-height: 400px; overflow-y: auto;">
          <div
            v-for="notification in notifications"
            :key="notification.id"
            class="notification-item"
            :class="{ 'unread': !notification.read }"
            @click="handleNotificationClick(notification)"
          >
            <div class="d-flex align-items-start p-3">
              <div class="notification-icon me-3">
                <i :class="getNotificationIcon(notification.type)" :style="{ color: getNotificationColor(notification.severity) }"></i>
              </div>
              <div class="flex-grow-1">
                <h6 class="notification-title mb-1">{{ notification.title }}</h6>
                <p class="notification-message mb-1 text-muted small">{{ notification.message }}</p>
                <small class="text-muted">{{ formatTime(notification.timestamp) }}</small>
              </div>
              <div class="notification-actions">
                <button
                  class="btn btn-sm btn-link text-muted p-0"
                  @click.stop="removeNotification(notification.id)"
                >
                  <i class="bi bi-x"></i>
                </button>
              </div>
            </div>
          </div>

          <!-- Empty State -->
          <div v-if="notifications.length === 0" class="text-center py-4">
            <i class="bi bi-bell-slash text-muted" style="font-size: 2rem;"></i>
            <p class="text-muted mt-2 mb-0">暂无通知</p>
          </div>
        </div>

        <!-- View All Link -->
        <div v-if="notifications.length > 5" class="dropdown-divider"></div>
        <div v-if="notifications.length > 5" class="text-center p-2">
          <router-link to="/notifications" class="btn btn-sm btn-link text-decoration-none">
            查看全部通知
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { notificationService } from '@/services/notificationService'

export default {
  name: 'NotificationCenter',
  setup() {
    const notifications = computed(() => notificationService.notifications.value.slice(0, 10))
    const unreadCount = computed(() => notificationService.unreadCount.value)

    const markAllAsRead = () => {
      notificationService.markAllAsRead()
    }

    const clearAll = () => {
      notificationService.clearAll()
    }

    const removeNotification = (id) => {
      notificationService.removeNotification(id)
    }

    const handleNotificationClick = (notification) => {
      notificationService.markAsRead(notification.id)
      if (notification.action) {
        notification.action()
      }
    }

    const getNotificationIcon = (type) => {
      const iconMap = {
        patent_approved: 'bi bi-check-circle-fill',
        patent_rejected: 'bi bi-x-circle-fill',
        patent_sold: 'bi bi-currency-exchange',
        patent_purchased: 'bi bi-bag-check-fill',
        rights_protection_initiated: 'bi bi-shield-exclamation',
        rights_protection_resolved: 'bi bi-shield-check',
        transaction_completed: 'bi bi-check2-circle',
        review_assigned: 'bi bi-clipboard-check'
      }
      return iconMap[type] || 'bi bi-info-circle-fill'
    }

    const getNotificationColor = (severity) => {
      const colorMap = {
        success: '#198754',
        error: '#dc3545',
        warning: '#fd7e14',
        info: '#0dcaf0'
      }
      return colorMap[severity] || '#6c757d'
    }

    const formatTime = (timestamp) => {
      const now = new Date()
      const time = new Date(timestamp)
      const diffInMinutes = Math.floor((now - time) / (1000 * 60))

      if (diffInMinutes < 1) {
        return '刚刚'
      } else if (diffInMinutes < 60) {
        return `${diffInMinutes}分钟前`
      } else if (diffInMinutes < 1440) {
        const hours = Math.floor(diffInMinutes / 60)
        return `${hours}小时前`
      } else {
        const days = Math.floor(diffInMinutes / 1440)
        return `${days}天前`
      }
    }

    return {
      notifications,
      unreadCount,
      markAllAsRead,
      clearAll,
      removeNotification,
      handleNotificationClick,
      getNotificationIcon,
      getNotificationColor,
      formatTime
    }
  }
}
</script>

<style scoped>
.notification-center {
  position: relative;
}

.notification-dropdown {
  border: none;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  border-radius: 0.5rem;
}

.notification-item {
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid #f8f9fa;
}

.notification-item:hover {
  background-color: #f8f9fa;
}

.notification-item.unread {
  background-color: #e3f2fd;
  border-left: 3px solid #2196f3;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-icon {
  font-size: 1.2rem;
}

.notification-title {
  font-size: 0.9rem;
  font-weight: 600;
}

.notification-message {
  font-size: 0.8rem;
  line-height: 1.3;
}

.notification-actions {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.notification-item:hover .notification-actions {
  opacity: 1;
}

.dropdown-header {
  padding: 0.75rem 1rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.notification-list {
  padding: 0;
}

.badge {
  font-size: 0.6rem;
  transform: translate(25%, -25%);
}
</style>
