<template>
  <div class="admin-users-view">
    <div class="container-fluid py-4">
      <!-- Header -->
      <div class="d-flex align-items-center justify-content-between mb-4">
        <div class="d-flex align-items-center">
          <i class="bi bi-people text-primary me-3" style="font-size: 2rem;"></i>
          <div>
            <h2 class="mb-1">用户管理</h2>
            <p class="text-muted mb-0">管理系统用户和角色权限</p>
          </div>
        </div>
        <button class="btn btn-primary" @click="refreshUsers">
          <i class="bi bi-arrow-clockwise me-2"></i>
          刷新
        </button>
      </div>

      <!-- Statistics Cards -->
      <div class="row g-4 mb-4">
        <div class="col-md-3">
          <div class="card border-0 shadow-sm">
            <div class="card-body">
              <div class="d-flex align-items-center">
                <div class="flex-shrink-0">
                  <div class="bg-primary bg-opacity-10 rounded-3 p-3">
                    <i class="bi bi-people text-primary" style="font-size: 1.5rem;"></i>
                  </div>
                </div>
                <div class="flex-grow-1 ms-3">
                  <h6 class="text-muted mb-1">总用户数</h6>
                  <h3 class="mb-0">{{ statistics.totalUsers }}</h3>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card border-0 shadow-sm">
            <div class="card-body">
              <div class="d-flex align-items-center">
                <div class="flex-shrink-0">
                  <div class="bg-success bg-opacity-10 rounded-3 p-3">
                    <i class="bi bi-person-check text-success" style="font-size: 1.5rem;"></i>
                  </div>
                </div>
                <div class="flex-grow-1 ms-3">
                  <h6 class="text-muted mb-1">活跃用户</h6>
                  <h3 class="mb-0">{{ statistics.activeUsers }}</h3>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card border-0 shadow-sm">
            <div class="card-body">
              <div class="d-flex align-items-center">
                <div class="flex-shrink-0">
                  <div class="bg-info bg-opacity-10 rounded-3 p-3">
                    <i class="bi bi-person-plus text-info" style="font-size: 1.5rem;"></i>
                  </div>
                </div>
                <div class="flex-grow-1 ms-3">
                  <h6 class="text-muted mb-1">本月新增</h6>
                  <h3 class="mb-0">{{ statistics.newUsersThisMonth }}</h3>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card border-0 shadow-sm">
            <div class="card-body">
              <div class="d-flex align-items-center">
                <div class="flex-shrink-0">
                  <div class="bg-warning bg-opacity-10 rounded-3 p-3">
                    <i class="bi bi-shield-check text-warning" style="font-size: 1.5rem;"></i>
                  </div>
                </div>
                <div class="flex-grow-1 ms-3">
                  <h6 class="text-muted mb-1">审核员</h6>
                  <h3 class="mb-0">{{ statistics.usersByRole.reviewer }}</h3>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Search and Filter -->
      <div class="card shadow-sm mb-4">
        <div class="card-body">
          <div class="row g-3">
            <div class="col-md-4">
              <label class="form-label">搜索用户</label>
              <div class="input-group">
                <span class="input-group-text">
                  <i class="bi bi-search"></i>
                </span>
                <input
                  type="text"
                  class="form-control"
                  placeholder="姓名、手机号或地址"
                  v-model="searchQuery"
                  @input="filterUsers"
                >
              </div>
            </div>
            <div class="col-md-3">
              <label class="form-label">角色筛选</label>
              <select class="form-select" v-model="roleFilter" @change="filterUsers">
                <option value="">全部角色</option>
                <option value="user">普通用户</option>
                <option value="reviewer">审核员</option>
                <option value="admin">管理员</option>
              </select>
            </div>
            <div class="col-md-3">
              <label class="form-label">状态筛选</label>
              <select class="form-select" v-model="statusFilter" @change="filterUsers">
                <option value="">全部状态</option>
                <option value="active">活跃</option>
                <option value="inactive">非活跃</option>
              </select>
            </div>
            <div class="col-md-2">
              <label class="form-label">&nbsp;</label>
              <button class="btn btn-outline-secondary w-100" @click="clearFilters">
                <i class="bi bi-x-circle me-2"></i>
                清空
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Users Table -->
      <div class="card shadow-sm">
        <div class="card-header bg-white">
          <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">用户列表</h5>
            <span class="badge bg-primary">{{ filteredUsers.length }} 个用户</span>
          </div>
        </div>
        <div class="card-body p-0">
          <div v-if="isLoading" class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">加载中...</span>
            </div>
            <p class="text-muted mt-2">加载用户数据中...</p>
          </div>

          <div v-else-if="error" class="alert alert-danger m-3">
            <i class="bi bi-exclamation-triangle me-2"></i>
            {{ error }}
          </div>

          <div v-else-if="filteredUsers.length === 0" class="text-center py-5">
            <i class="bi bi-people text-muted" style="font-size: 3rem;"></i>
            <h5 class="text-muted mt-3">未找到用户</h5>
            <p class="text-muted">请调整搜索条件或筛选器</p>
          </div>

          <div v-else class="table-responsive">
            <table class="table table-hover mb-0">
              <thead class="table-light">
                <tr>
                  <th>用户信息</th>
                  <th>联系方式</th>
                  <th>角色</th>
                  <th>状态</th>
                  <th>注册时间</th>
                  <th>最后登录</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="user in filteredUsers" :key="user.address">
                  <td>
                    <div class="d-flex align-items-center">
                      <div class="avatar-circle me-3">
                        {{ user.name.charAt(0) }}
                      </div>
                      <div>
                        <h6 class="mb-1">{{ user.name }}</h6>
                        <small class="text-muted">
                          {{ formatAddress(user.address) }}
                        </small>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div>
                      <div class="mb-1">
                        <i class="bi bi-telephone me-1"></i>
                        {{ user.phone }}
                      </div>
                      <small class="text-muted">
                        身份证: {{ maskIdNumber(user.idNumber) }}
                      </small>
                    </div>
                  </td>
                  <td>
                    <span :class="getRoleBadgeClass(user.role)" class="badge">
                      {{ getRoleText(user.role) }}
                    </span>
                  </td>
                  <td>
                    <span :class="getStatusBadgeClass(user.status)" class="badge">
                      {{ getStatusText(user.status) }}
                    </span>
                  </td>
                  <td>
                    <small>{{ formatDate(user.registrationDate) }}</small>
                  </td>
                  <td>
                    <small>{{ formatDate(user.lastLoginDate) }}</small>
                  </td>
                  <td>
                    <div class="btn-group btn-group-sm">
                      <button
                        class="btn btn-outline-primary"
                        @click="viewUserDetails(user)"
                        title="查看详情"
                      >
                        <i class="bi bi-eye"></i>
                      </button>
                      <button
                        class="btn btn-outline-warning"
                        @click="editUserRole(user)"
                        title="编辑角色"
                        :disabled="user.address === authStore.account"
                      >
                        <i class="bi bi-pencil"></i>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- User Details Modal -->
    <div class="modal fade" id="userDetailsModal" tabindex="-1">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">用户详情</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body" v-if="selectedUser">
            <div class="row g-3">
              <div class="col-md-6">
                <label class="form-label">姓名</label>
                <p class="form-control-plaintext">{{ selectedUser.name }}</p>
              </div>
              <div class="col-md-6">
                <label class="form-label">手机号</label>
                <p class="form-control-plaintext">{{ selectedUser.phone }}</p>
              </div>
              <div class="col-md-6">
                <label class="form-label">身份证号</label>
                <p class="form-control-plaintext">{{ selectedUser.idNumber }}</p>
              </div>
              <div class="col-md-6">
                <label class="form-label">区块链地址</label>
                <p class="form-control-plaintext">
                  <code>{{ selectedUser.address }}</code>
                </p>
              </div>
              <div class="col-md-6">
                <label class="form-label">角色</label>
                <p class="form-control-plaintext">
                  <span :class="getRoleBadgeClass(selectedUser.role)" class="badge">
                    {{ getRoleText(selectedUser.role) }}
                  </span>
                </p>
              </div>
              <div class="col-md-6">
                <label class="form-label">状态</label>
                <p class="form-control-plaintext">
                  <span :class="getStatusBadgeClass(selectedUser.status)" class="badge">
                    {{ getStatusText(selectedUser.status) }}
                  </span>
                </p>
              </div>
              <div class="col-md-6">
                <label class="form-label">注册时间</label>
                <p class="form-control-plaintext">{{ formatDate(selectedUser.registrationDate) }}</p>
              </div>
              <div class="col-md-6">
                <label class="form-label">最后登录</label>
                <p class="form-control-plaintext">{{ formatDate(selectedUser.lastLoginDate) }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Role Edit Modal -->
    <div class="modal fade" id="roleEditModal" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">编辑用户角色</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body" v-if="selectedUser">
            <div class="mb-3">
              <label class="form-label">用户</label>
              <p class="form-control-plaintext">{{ selectedUser.name }} ({{ formatAddress(selectedUser.address) }})</p>
            </div>
            <div class="mb-3">
              <label class="form-label">当前角色</label>
              <p class="form-control-plaintext">
                <span :class="getRoleBadgeClass(selectedUser.role)" class="badge">
                  {{ getRoleText(selectedUser.role) }}
                </span>
              </p>
            </div>
            <div class="mb-3">
              <label for="newRole" class="form-label">新角色 <span class="text-danger">*</span></label>
              <select
                id="newRole"
                class="form-select"
                v-model="newRole"
                :class="{ 'is-invalid': roleEditError }"
              >
                <option value="">请选择角色</option>
                <option value="user">普通用户</option>
                <option value="reviewer">审核员</option>
                <option value="admin">管理员</option>
              </select>
              <div v-if="roleEditError" class="invalid-feedback">
                {{ roleEditError }}
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
              取消
            </button>
            <button
              type="button"
              class="btn btn-primary"
              @click="updateUserRole"
              :disabled="isUpdatingRole || !newRole"
            >
              <span v-if="isUpdatingRole" class="spinner-border spinner-border-sm me-2"></span>
              {{ isUpdatingRole ? '更新中...' : '确认更新' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { userService } from '@/services/userService'
import { notificationService } from '@/services/notificationService'

export default {
  name: 'AdminUsersView',
  setup() {
    const authStore = useAuthStore()

    // State
    const users = ref([])
    const statistics = ref({})
    const isLoading = ref(false)
    const error = ref(null)
    const selectedUser = ref(null)
    const newRole = ref('')
    const roleEditError = ref('')
    const isUpdatingRole = ref(false)

    // Filters
    const searchQuery = ref('')
    const roleFilter = ref('')
    const statusFilter = ref('')

    // Computed
    const filteredUsers = computed(() => {
      let filtered = users.value

      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase()
        filtered = filtered.filter(user =>
          user.name.toLowerCase().includes(query) ||
          user.phone.includes(query) ||
          user.address.toLowerCase().includes(query)
        )
      }

      if (roleFilter.value) {
        filtered = filtered.filter(user => user.role === roleFilter.value)
      }

      if (statusFilter.value) {
        filtered = filtered.filter(user => user.status === statusFilter.value)
      }

      return filtered
    })

    // Methods
    const loadUsers = async () => {
      try {
        isLoading.value = true
        error.value = null

        const [usersData, statsData] = await Promise.all([
          userService.getAllUsers(),
          userService.getUserStatistics()
        ])

        users.value = usersData
        statistics.value = statsData

      } catch (err) {
        error.value = err.message
      } finally {
        isLoading.value = false
      }
    }

    const refreshUsers = () => {
      loadUsers()
    }

    const filterUsers = () => {
      // Filtering is handled by computed property
    }

    const clearFilters = () => {
      searchQuery.value = ''
      roleFilter.value = ''
      statusFilter.value = ''
    }

    const viewUserDetails = (user) => {
      selectedUser.value = user
      const modal = new bootstrap.Modal(document.getElementById('userDetailsModal'))
      modal.show()
    }

    const editUserRole = (user) => {
      selectedUser.value = user
      newRole.value = user.role
      roleEditError.value = ''
      const modal = new bootstrap.Modal(document.getElementById('roleEditModal'))
      modal.show()
    }

    const updateUserRole = async () => {
      try {
        isUpdatingRole.value = true
        roleEditError.value = ''

        // Validate role change
        const validation = userService.validateRoleChange(
          selectedUser.value.role,
          newRole.value,
          authStore.userRole
        )

        if (!validation.isValid) {
          roleEditError.value = validation.errors[0]
          return
        }

        await userService.updateUserRole(selectedUser.value.address, newRole.value)

        // Update local data
        const userIndex = users.value.findIndex(u => u.address === selectedUser.value.address)
        if (userIndex !== -1) {
          users.value[userIndex].role = newRole.value
        }

        // Show success notification
        notificationService.addNotification({
          type: 'success',
          title: '角色更新成功',
          message: `用户 ${selectedUser.value.name} 的角色已更新为 ${getRoleText(newRole.value)}`,
          severity: 'success'
        })

        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('roleEditModal'))
        modal.hide()

      } catch (err) {
        roleEditError.value = err.message
      } finally {
        isUpdatingRole.value = false
      }
    }

    // Utility functions
    const formatAddress = (address) => {
      return `${address.slice(0, 6)}...${address.slice(-4)}`
    }

    const maskIdNumber = (idNumber) => {
      return `${idNumber.slice(0, 6)}****${idNumber.slice(-4)}`
    }

    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleDateString('zh-CN')
    }

    const getRoleText = (role) => {
      const roleMap = {
        user: '普通用户',
        reviewer: '审核员',
        admin: '管理员'
      }
      return roleMap[role] || '未知'
    }

    const getRoleBadgeClass = (role) => {
      const classMap = {
        user: 'bg-primary',
        reviewer: 'bg-warning',
        admin: 'bg-danger'
      }
      return classMap[role] || 'bg-secondary'
    }

    const getStatusText = (status) => {
      const statusMap = {
        active: '活跃',
        inactive: '非活跃'
      }
      return statusMap[status] || '未知'
    }

    const getStatusBadgeClass = (status) => {
      const classMap = {
        active: 'bg-success',
        inactive: 'bg-secondary'
      }
      return classMap[status] || 'bg-secondary'
    }

    // Lifecycle
    onMounted(() => {
      loadUsers()
    })

    return {
      authStore,
      users,
      statistics,
      isLoading,
      error,
      selectedUser,
      newRole,
      roleEditError,
      isUpdatingRole,
      searchQuery,
      roleFilter,
      statusFilter,
      filteredUsers,
      loadUsers,
      refreshUsers,
      filterUsers,
      clearFilters,
      viewUserDetails,
      editUserRole,
      updateUserRole,
      formatAddress,
      maskIdNumber,
      formatDate,
      getRoleText,
      getRoleBadgeClass,
      getStatusText,
      getStatusBadgeClass
    }
  }
}
</script>

<style scoped>
.admin-users-view {
  background-color: #f8f9fa;
  min-height: 100vh;
}

.avatar-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 1.1rem;
}

.card {
  border: none;
  border-radius: 12px;
}

.card-header {
  border-radius: 12px 12px 0 0 !important;
  border-bottom: 1px solid #e9ecef;
}

.table th {
  border-top: none;
  font-weight: 600;
  color: #495057;
  font-size: 0.875rem;
}

.table td {
  vertical-align: middle;
  border-color: #f8f9fa;
}

.btn-group-sm .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

.badge {
  font-size: 0.75rem;
  padding: 0.35em 0.65em;
}

.modal-content {
  border: none;
  border-radius: 12px;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modal-header {
  border-bottom: 1px solid #e9ecef;
  border-radius: 12px 12px 0 0;
}

.form-control, .form-select {
  border-radius: 8px;
}

.btn {
  border-radius: 8px;
}

.input-group-text {
  border-radius: 8px 0 0 8px;
}

.input-group .form-control {
  border-radius: 0 8px 8px 0;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

@media (max-width: 768px) {
  .table-responsive {
    font-size: 0.875rem;
  }

  .btn-group-sm .btn {
    padding: 0.2rem 0.4rem;
    font-size: 0.7rem;
  }

  .avatar-circle {
    width: 32px;
    height: 32px;
    font-size: 0.9rem;
  }
}
</style>